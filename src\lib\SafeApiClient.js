/**
 * Safe API Client
 * Optimized client for making safe API calls with built-in rate limiting protection
 */

class SafeApiClient {
  constructor(baseURL = 'http://localhost:3000/api') {
    this.baseURL = baseURL;
    this.requestQueue = [];
    this.isProcessing = false;
    this.cache = new Map();
    this.cacheTTL = 60000; // 1 minute default cache
    
    // Clean cache periodically
    setInterval(() => this.cleanCache(), 30000);
  }

  /**
   * Make a safe GET request with built-in optimizations
   */
  async get(endpoint, options = {}) {
    const cacheKey = `GET:${endpoint}:${JSON.stringify(options.params || {})}`;
    
    // Check cache first
    if (options.useCache !== false) {
      const cached = this.getCached(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const response = await this.makeRequest('GET', endpoint, options);
      
      // Cache successful responses
      if (response.success && options.useCache !== false) {
        this.setCache(cacheKey, response, options.cacheTTL || this.cacheTTL);
      }
      
      return response;
    } catch (error) {
      console.error(`API Error for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make multiple GET requests using batch API
   */
  async batchGet(requests) {
    const batchPayload = {
      requests: requests.map((req, index) => ({
        id: req.id || `request_${index}`,
        method: 'GET',
        endpoint: req.endpoint,
        params: req.params || {},
        query: req.query || {}
      }))
    };

    try {
      const response = await fetch(`${this.baseURL}/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(batchPayload)
      });

      if (!response.ok) {
        throw new Error(`Batch request failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Cache individual results
      if (result.success && result.results) {
        result.results.forEach((item, index) => {
          if (item.success) {
            const originalRequest = requests[index];
            const cacheKey = `GET:${originalRequest.endpoint}:${JSON.stringify(originalRequest.params || {})}`;
            this.setCache(cacheKey, item.data, originalRequest.cacheTTL || this.cacheTTL);
          }
        });
      }
      
      return result;
    } catch (error) {
      console.error('Batch API Error:', error);
      throw error;
    }
  }

  /**
   * Make individual request with rate limiting protection
   */
  async makeRequest(method, endpoint, options = {}) {
    let url = `${this.baseURL}${endpoint}`;
    const config = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      credentials: 'include'
    };

    // Add query parameters for GET requests
    if (method === 'GET' && options.params) {
      const searchParams = new URLSearchParams(options.params);
      const separator = url.includes('?') ? '&' : '?';
      url += separator + searchParams.toString();
    }

    const response = await fetch(url, config);
    
    // Handle rate limiting
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After') || '60';
      const delay = parseInt(retryAfter) * 1000;
      
      console.warn(`Rate limited. Retrying after ${retryAfter} seconds...`);
      await this.delay(delay);
      
      // Retry the request
      return this.makeRequest(method, endpoint, options);
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Cache management
   */
  getCached(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() < cached.expires) {
      return cached.data;
    }
    return null;
  }

  setCache(key, data, ttl = this.cacheTTL) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now >= value.expires) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Utility methods
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Convenience methods for common endpoints
   */
  async getBrands(useCache = true) {
    return this.get('/brands', { useCache });
  }

  async getBrand(id, useCache = true) {
    return this.get(`/brands/${id}`, { useCache });
  }

  async getCategories(useCache = true) {
    return this.get('/categories', { useCache });
  }

  async getCategory(id, useCache = true) {
    return this.get(`/categories/${id}`, { useCache });
  }

  async getProducts(params = {}, useCache = true) {
    return this.get('/products', { params, useCache });
  }

  async getProduct(id, useCache = true) {
    return this.get(`/products/${id}`, { useCache });
  }

  async getProductVariants(productId, useCache = true) {
    return this.get(`/products/${productId}/variants`, { useCache, cacheTTL: 30000 }); // 30s cache
  }

  async getProductPhotos(productId, useCache = true) {
    return this.get(`/products/${productId}/photos`, { useCache, cacheTTL: 30000 }); // 30s cache
  }

  async getProductRatings(productId, useCache = true) {
    return this.get(`/products/${productId}/ratings`, { useCache, cacheTTL: 30000 }); // 30s cache
  }

  async getBanners(useCache = true) {
    return this.get('/banners', { useCache });
  }

  async getActiveBanners(useCache = true) {
    return this.get('/banners/active', { useCache });
  }

  /**
   * Batch convenience method for common data loading
   */
  async loadInitialData() {
    return this.batchGet([
      { id: 'brands', endpoint: '/api/brands' },
      { id: 'categories', endpoint: '/api/categories' },
      { id: 'banners', endpoint: '/api/banners/active' },
      { id: 'highSalesProducts', endpoint: '/api/products/high-sales' },
      { id: 'highRatingProducts', endpoint: '/api/products/high-rating' }
    ]);
  }

  /**
   * Load product details with all related data
   */
  async loadProductDetails(productId) {
    return this.batchGet([
      { id: 'product', endpoint: '/api/products/:id', params: { id: productId } },
      { id: 'variants', endpoint: '/api/products/:productId/variants', params: { productId } },
      { id: 'photos', endpoint: '/api/products/:productId/photos', params: { productId } },
      { id: 'ratings', endpoint: '/api/products/:productId/ratings', params: { productId } }
    ]);
  }
}

// Export singleton instance
const safeApiClient = new SafeApiClient();
export default safeApiClient;
