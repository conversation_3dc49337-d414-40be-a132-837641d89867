/**
 * Request Optimization Middleware
 * Provides request deduplication, throttling, and optimization features
 */

// In-memory cache for request deduplication (in production, use Redis)
const requestCache = new Map();
const CACHE_TTL = 5000; // 5 seconds

// Clean up expired cache entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of requestCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      requestCache.delete(key);
    }
  }
}, 30000); // Clean every 30 seconds

// Request deduplication middleware
const deduplicateRequests = (req, res, next) => {
  // Only apply to GET requests
  if (req.method !== 'GET') {
    return next();
  }

  // Create cache key from method, path, and query parameters
  const cacheKey = `${req.method}:${req.path}:${JSON.stringify(req.query)}:${req.ip}`;
  const now = Date.now();
  
  // Check if we have a recent identical request
  const cached = requestCache.get(cacheKey);
  if (cached && (now - cached.timestamp) < CACHE_TTL) {
    // Return cached response
    res.status(cached.status).json(cached.data);
    return;
  }

  // Store original res.json to intercept response
  const originalJson = res.json;
  res.json = function(data) {
    // Cache successful responses
    if (res.statusCode >= 200 && res.statusCode < 300) {
      requestCache.set(cacheKey, {
        status: res.statusCode,
        data: data,
        timestamp: now
      });
    }
    
    return originalJson.call(this, data);
  };

  next();
};

// Request throttling middleware for high-frequency endpoints
const throttleRequests = (delayMs = 100) => {
  const requestTimes = new Map();
  
  return (req, res, next) => {
    const clientKey = req.ip;
    const now = Date.now();
    const lastRequest = requestTimes.get(clientKey);
    
    if (lastRequest && (now - lastRequest) < delayMs) {
      // Add small delay to prevent rapid-fire requests
      setTimeout(() => {
        requestTimes.set(clientKey, now);
        next();
      }, delayMs - (now - lastRequest));
    } else {
      requestTimes.set(clientKey, now);
      next();
    }
  };
};

// Request batching helper (for client-side implementation)
const batchingHeaders = (req, res, next) => {
  // Add headers to suggest batching capabilities
  res.set({
    'X-Supports-Batching': 'true',
    'X-Batch-Endpoint': '/api/batch',
    'X-Max-Batch-Size': '10'
  });
  next();
};

// Compression optimization
const optimizeResponse = (req, res, next) => {
  // Add response optimization headers
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  });
  
  // Enable compression hints
  if (req.headers['accept-encoding'] && req.headers['accept-encoding'].includes('gzip')) {
    res.set('Vary', 'Accept-Encoding');
  }
  
  next();
};

// Request monitoring and logging
const monitorRequests = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request details
  console.log(`📊 ${req.method} ${req.path} - IP: ${req.ip} - ${new Date().toISOString()}`);
  
  // Monitor response time
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    if (duration > 1000) { // Log slow requests
      console.warn(`⚠️  Slow request: ${req.method} ${req.path} took ${duration}ms`);
    }
  });
  
  next();
};

// Rate limit bypass for trusted sources (optional)
const trustedSourceBypass = (trustedIPs = []) => {
  return (req, res, next) => {
    if (trustedIPs.includes(req.ip)) {
      // Add header to indicate trusted source
      req.isTrustedSource = true;
      res.set('X-Trusted-Source', 'true');
    }
    next();
  };
};

module.exports = {
  deduplicateRequests,
  throttleRequests,
  batchingHeaders,
  optimizeResponse,
  monitorRequests,
  trustedSourceBypass
};
