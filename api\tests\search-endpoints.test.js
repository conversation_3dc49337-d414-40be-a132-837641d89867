/**
 * Test file for new search and view-based endpoints
 * Run with: node api/tests/search-endpoints.test.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

// Helper function to run a test
async function runTest(testName, testFunction) {
  testResults.total++;
  console.log(`\n🧪 Running: ${testName}`);
  
  try {
    await testFunction();
    testResults.passed++;
    console.log(`✅ PASSED: ${testName}`);
  } catch (error) {
    testResults.failed++;
    console.log(`❌ FAILED: ${testName}`);
    console.log(`   Error: ${error.message}`);
  }
}

// Test 1: Search Products Endpoint
async function testSearchProducts() {
  const response = await axios.get(`${BASE_URL}/products/search`, {
    params: { q: 'case' },
    ...TEST_CONFIG
  });
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Response success should be true');
  }
  
  if (!Array.isArray(response.data.data)) {
    throw new Error('Response data should be an array');
  }
  
  if (!response.data.search) {
    throw new Error('Response should include search metadata');
  }
  
  if (!response.data.pagination) {
    throw new Error('Response should include pagination metadata');
  }
  
  console.log(`   Found ${response.data.data.length} products for search "case"`);
}

// Test 2: Search with Filters
async function testSearchWithFilters() {
  const response = await axios.get(`${BASE_URL}/products/search`, {
    params: { 
      q: 'phone',
      min_price: 100000,
      max_price: 500000,
      sort_by: 'price_asc'
    },
    ...TEST_CONFIG
  });
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Response success should be true');
  }
  
  console.log(`   Found ${response.data.data.length} filtered products`);
}

// Test 3: View-Based Products Endpoint
async function testViewBasedProducts() {
  const response = await axios.get(`${BASE_URL}/products/view`, {
    params: { 
      offset: 0,
      limit: 10
    },
    ...TEST_CONFIG
  });
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Response success should be true');
  }
  
  if (!Array.isArray(response.data.data)) {
    throw new Error('Response data should be an array');
  }
  
  if (!response.data.meta) {
    throw new Error('Response should include meta information');
  }
  
  if (typeof response.data.meta.hasMore !== 'boolean') {
    throw new Error('Meta should include hasMore boolean');
  }
  
  console.log(`   Loaded ${response.data.data.length} products for view`);
  console.log(`   Has more: ${response.data.meta.hasMore}`);
}

// Test 4: View-Based Products with Pagination
async function testViewBasedPagination() {
  const response = await axios.get(`${BASE_URL}/products/view`, {
    params: { 
      offset: 10,
      limit: 5
    },
    ...TEST_CONFIG
  });
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Response success should be true');
  }
  
  if (response.data.meta.offset !== 10) {
    throw new Error('Meta offset should match request');
  }
  
  console.log(`   Loaded page with offset ${response.data.meta.offset}`);
}

// Test 5: Search Error Handling
async function testSearchErrorHandling() {
  try {
    await axios.get(`${BASE_URL}/products/search`, {
      params: { q: 'a' }, // Too short query
      ...TEST_CONFIG
    });
    throw new Error('Should have failed with short query');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('   Correctly rejected short search query');
    } else {
      throw error;
    }
  }
}

// Test 6: Rate Limiting Protection
async function testRateLimiting() {
  // Make multiple rapid requests to test rate limiting
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(
      axios.get(`${BASE_URL}/products/view`, {
        params: { offset: 0, limit: 1 },
        ...TEST_CONFIG
      })
    );
  }
  
  const responses = await Promise.all(promises);
  
  // All should succeed due to SafeApi optimizations
  responses.forEach((response, index) => {
    if (response.status !== 200) {
      throw new Error(`Request ${index + 1} failed with status ${response.status}`);
    }
  });
  
  console.log('   All rapid requests handled successfully');
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting API Endpoint Tests\n');
  console.log('Make sure the API server is running on http://localhost:3000');
  
  // Wait a moment for server to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await runTest('Search Products Basic', testSearchProducts);
  await runTest('Search with Filters', testSearchWithFilters);
  await runTest('View-Based Products', testViewBasedProducts);
  await runTest('View-Based Pagination', testViewBasedPagination);
  await runTest('Search Error Handling', testSearchErrorHandling);
  await runTest('Rate Limiting Protection', testRateLimiting);
  
  // Print summary
  console.log('\n📊 Test Summary:');
  console.log(`   Total Tests: ${testResults.total}`);
  console.log(`   Passed: ${testResults.passed}`);
  console.log(`   Failed: ${testResults.failed}`);
  console.log(`   Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the API implementation.');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('\n💥 Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runAllTests };
