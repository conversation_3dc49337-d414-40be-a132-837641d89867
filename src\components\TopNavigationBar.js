import '../styles/components/top-navigation-bar.css';
import logo from '../../public/assets/logoCatalog.png';

export const TopNavigationBar = () => {
    return `
        <header class="topNavigationBar">
            <div class="navigationBarContainer">
                <div class="logoNavigationBar">
                    <a href="/">
                    <img src="${logo}" alt="${logo}" class="logo-image">
                    </a>
                </div>

                <nav>
                    <div class="nav-links">
                        <a href="/" class="">Home</a>
                        <a href="/katalog" class="">Katalog</a>
                        <a href="/brands" class="">Brands</a>
                        <a href="/wishlist" class="">Wishlist</a>
                    </div>
                </nav>

                <div class="search-bar">
                    <input type="text" id="search-input" placeholder="Cari Barang...." autocomplete="off">
                    <button id="search-button" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="search-suggestions" id="search-suggestions" style="display: none;"></div>
                </div>
            </div>
        </header>
    `;
};

// Search functionality class
export class SearchHandler {
    constructor() {
        this.searchInput = null;
        this.searchButton = null;
        this.suggestionsContainer = null;
        this.searchTimeout = null;
        this.currentSearchQuery = '';
        this.isSearching = false;
    }

    init() {
        this.searchInput = document.getElementById('search-input');
        this.searchButton = document.getElementById('search-button');
        this.suggestionsContainer = document.getElementById('search-suggestions');

        if (!this.searchInput || !this.searchButton || !this.suggestionsContainer) {
            console.warn('Search elements not found');
            return;
        }

        this.bindEvents();
    }

    bindEvents() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(e.target.value);
            } else if (e.key === 'Escape') {
                this.hideSuggestions();
            }
        });

        this.searchInput.addEventListener('focus', () => {
            if (this.currentSearchQuery) {
                this.showSuggestions();
            }
        });

        this.searchInput.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => this.hideSuggestions(), 150);
        });

        // Search button event
        this.searchButton.addEventListener('click', () => {
            this.performSearch(this.searchInput.value);
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-bar')) {
                this.hideSuggestions();
            }
        });
    }

    async handleSearchInput(query) {
        const trimmedQuery = query.trim();

        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Hide suggestions if query is too short
        if (trimmedQuery.length < 2) {
            this.hideSuggestions();
            this.currentSearchQuery = '';
            return;
        }

        // Debounce search suggestions
        this.searchTimeout = setTimeout(async () => {
            await this.fetchSearchSuggestions(trimmedQuery);
        }, 300);
    }

    async fetchSearchSuggestions(query) {
        if (this.isSearching || query === this.currentSearchQuery) {
            return;
        }

        try {
            this.isSearching = true;
            this.currentSearchQuery = query;

            // Import SafeApiClient dynamically to avoid circular imports
            const { default: safeApiClient } = await import('../lib/SafeApiClient.js');

            const response = await safeApiClient.get('/products/search', {
                params: { q: query, limit: 5 },
                useCache: true,
                cacheTTL: 30000 // 30 seconds cache for suggestions
            });

            if (response.success && response.data.length > 0) {
                this.displaySuggestions(response.data, query);
            } else {
                this.hideSuggestions();
            }

        } catch (error) {
            console.error('Error fetching search suggestions:', error);
            this.hideSuggestions();
        } finally {
            this.isSearching = false;
        }
    }

    displaySuggestions(products, query) {
        if (!products || products.length === 0) {
            this.hideSuggestions();
            return;
        }

        const suggestionsHTML = products.map(product => `
            <div class="search-suggestion-item" data-product-id="${product.id}">
                <img src="${product.photo_url || '/assets/placeholder.png'}" alt="${product.name}" class="suggestion-image">
                <div class="suggestion-content">
                    <div class="suggestion-name">${this.highlightMatch(product.name, query)}</div>
                    <div class="suggestion-price">${this.formatRupiah(product.price)}</div>
                    <div class="suggestion-brand">${product.brand_name || ''}</div>
                </div>
            </div>
        `).join('');

        this.suggestionsContainer.innerHTML = `
            ${suggestionsHTML}
            <div class="search-suggestion-footer">
                <button class="view-all-results" data-query="${query}">
                    Lihat semua hasil untuk "${query}"
                </button>
            </div>
        `;

        this.bindSuggestionEvents();
        this.showSuggestions();
    }

    bindSuggestionEvents() {
        // Product suggestion clicks
        this.suggestionsContainer.querySelectorAll('.search-suggestion-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const productId = e.currentTarget.dataset.productId;
                this.navigateToProduct(productId);
            });
        });

        // View all results button
        const viewAllButton = this.suggestionsContainer.querySelector('.view-all-results');
        if (viewAllButton) {
            viewAllButton.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                this.performSearch(query);
            });
        }
    }

    performSearch(query) {
        const trimmedQuery = query.trim();

        if (trimmedQuery.length < 2) {
            alert('Masukkan minimal 2 karakter untuk pencarian');
            return;
        }

        this.hideSuggestions();

        // Navigate to search results page (katalog with search parameter)
        const searchParams = new URLSearchParams({ search: trimmedQuery });
        window.location.href = `/katalog?${searchParams.toString()}`;
    }

    navigateToProduct(productId) {
        this.hideSuggestions();
        // Navigate to product detail page
        window.location.href = `/product/${productId}`;
    }

    showSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'block';
        }
    }

    hideSuggestions() {
        if (this.suggestionsContainer) {
            this.suggestionsContainer.style.display = 'none';
        }
    }

    highlightMatch(text, query) {
        if (!query || !text) return text;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    formatRupiah(amount) {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(amount);
    }

    destroy() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
}
