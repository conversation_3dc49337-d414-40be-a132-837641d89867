.topNavigationBar {
  display: flex;
  background-color: black;
  padding: 1rem 1.25rem;
  width: 100%;
}

.navigationBarContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 1.5rem 0 1.5rem;
  gap: 20px;
  width: 100%;
}

.logoNavigationBar {
  width: 40px;
  height: 40px;
}

.topNavigationBar nav {
  padding: 0 0 0 200px;
  display: none;
}

.logo-image{
  width: 40px;
  height: 40px;
}

.search-bar {
  display: flex;
  overflow: visible;
  border-radius: 0.375rem;
  width: 100%;
  max-width: 300px;
  position: relative;
}

.search-bar input {
  background-color: #323232;
  padding: 0.5rem 1.25rem;
  color: #b7b7b7;
  border: none;
  width: 100%;
}

.search-bar input:focus {
  outline: none;
}

.search-bar button {
  background-color: #ffcd29;
  padding: 0.25rem 0.75rem;
  border: none;
  cursor: pointer;
}

.search-bar button i {
  color: black;
  font-size: 1.25rem;
}

/* Search Suggestions Styles */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
}

.search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-suggestion-item:hover {
  background-color: #f9fafb;
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.25rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-name {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-name mark {
  background-color: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

.suggestion-price {
  font-weight: 600;
  color: #059669;
  font-size: 0.75rem;
  line-height: 1rem;
  margin-bottom: 0.125rem;
}

.suggestion-brand {
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1rem;
}

.search-suggestion-footer {
  padding: 0.75rem;
  border-top: 1px solid #f3f4f6;
  background-color: #f9fafb;
}

.view-all-results {
  width: 100%;
  padding: 0.5rem;
  background-color: #ffcd29;
  color: black;
  border: none;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-all-results:hover {
  background-color: #fbbf24;
}

@media (min-width: 1280px) {
  .topNavigationBar {
    display: flex;
    align-items: center;
  }

  .topNavigationBar nav {
    padding: 0 0 0 200px;
    display: block;
  }

  .topNavigationBar .nav-links {
    display: flex;
    gap: 2.5rem;
    align-items: center;
  }

  .topNavigationBar .nav-links a {
    color: #838383;
    text-decoration: none;
    transition: color 0.3s;
  }

  .topNavigationBar .nav-links a.active {
    color: #f8be00;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 700px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1140px;
  }
}