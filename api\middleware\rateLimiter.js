const { rateLimit, ipKeyGenerator } = require('express-rate-limit');

// General API rate limiter
const generalLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 300,
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const publicReadLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 500,
  message: {
    success: false,
    error: 'Too many read requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: false,
  keyGenerator: ipKeyGenerator
});

const authenticatedReadLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 800,
  message: {
    success: false,
    error: 'Too many authenticated read requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const strictLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 50,
  message: {
    success: false,
    error: 'Too many write requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const adminReadLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 2000,
  message: {
    success: false,
    error: 'Too many admin read requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const adminLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 200,
  message: {
    success: false,
    error: 'Too many admin requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const ratingLimiter = rateLimit({
  windowMs: 60 * 60 * 1000,
  max: 5,
  message: {
    success: false,
    error: 'Too many rating submissions from this IP, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const adminLoginLimiter = rateLimit({
  windowMs: 1000 * 60 * 5,
  max: 5,
  message: {
    success: false,
    error: 'Too many login attempts from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
});

const testLimiter = rateLimit({
  windowMs: 5 * 60 * 1000,
  max: 10,
  message: {
    success: false,
    error: 'Too many test requests from this IP, please try again later.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

module.exports = {
  generalLimiter,
  publicReadLimiter,
  authenticatedReadLimiter,
  strictLimiter,
  adminReadLimiter,
  adminLimiter,
  adminLoginLimiter,
  ratingLimiter,
  testLimiter
};