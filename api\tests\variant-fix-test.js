/**
 * Test file to verify the variant CRUD fix
 * Run with: node api/tests/variant-fix-test.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Helper function to run a test
async function runTest(testName, testFunction) {
  console.log(`\n🧪 Testing: ${testName}`);
  
  try {
    await testFunction();
    console.log(`✅ PASSED: ${testName}`);
    return true;
  } catch (error) {
    console.log(`❌ FAILED: ${testName}`);
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 1: Get variants for a product
async function testGetVariants() {
  const response = await axios.get(`${BASE_URL}/products/1/variants`, TEST_CONFIG);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Response success should be true');
  }
  
  if (!Array.isArray(response.data.data)) {
    throw new Error('Response data should be an array');
  }
  
  console.log(`   Found ${response.data.data.length} variants for product 1`);
}

// Test 2: Test that the endpoint doesn't crash (main issue)
async function testEndpointStability() {
  // Make multiple requests to ensure no crashes
  for (let i = 0; i < 3; i++) {
    const response = await axios.get(`${BASE_URL}/products/1/variants`, TEST_CONFIG);
    
    if (response.status !== 200) {
      throw new Error(`Request ${i + 1} failed with status ${response.status}`);
    }
    
    if (!response.data.success) {
      throw new Error(`Request ${i + 1} returned success: false`);
    }
  }
  
  console.log('   Multiple requests handled successfully without crashes');
}

// Test 3: Test different product IDs
async function testDifferentProducts() {
  const productIds = [1, 2, 3];
  
  for (const productId of productIds) {
    try {
      const response = await axios.get(`${BASE_URL}/products/${productId}/variants`, TEST_CONFIG);
      
      if (response.status === 200 && response.data.success) {
        console.log(`   Product ${productId}: ${response.data.data.length} variants`);
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log(`   Product ${productId}: Not found (expected for non-existent products)`);
      } else {
        throw error;
      }
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Variant CRUD Fix Tests\n');
  console.log('Make sure the API server is running on http://localhost:3000');
  
  // Wait a moment for server to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  let passed = 0;
  let total = 0;
  
  total++; if (await runTest('Get Variants for Product', testGetVariants)) passed++;
  total++; if (await runTest('Endpoint Stability Test', testEndpointStability)) passed++;
  total++; if (await runTest('Different Product IDs', testDifferentProducts)) passed++;
  
  // Print summary
  console.log('\n📊 Test Summary:');
  console.log(`   Total Tests: ${total}`);
  console.log(`   Passed: ${passed}`);
  console.log(`   Failed: ${total - passed}`);
  console.log(`   Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! The variant CRUD fix is working correctly.');
    console.log('The "This command is not supported in the prepared statement protocol yet" error should be resolved.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the API implementation.');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('\n💥 Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runAllTests };
