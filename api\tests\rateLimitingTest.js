/**
 * Rate Limiting Test
 * Tests the improved rate limiting and safety features
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';
const TEST_ITERATIONS = 100;

// Test configuration
const tests = [
  {
    name: 'Public Brands Endpoint',
    endpoint: '/brands',
    expectedLimit: 500,
    description: 'Should allow 500 requests per 5 minutes'
  },
  {
    name: 'Public Products Endpoint',
    endpoint: '/products',
    expectedLimit: 500,
    description: 'Should allow 500 requests per 5 minutes'
  },
  {
    name: 'Batch API Endpoint',
    endpoint: '/batch',
    method: 'POST',
    data: {
      requests: [
        { id: 'brands', method: 'GET', endpoint: '/api/brands' },
        { id: 'categories', method: 'GET', endpoint: '/api/categories' }
      ]
    },
    expectedLimit: 500,
    description: 'Should handle batch requests efficiently'
  }
];

// Helper function to make requests
async function makeRequest(test, iteration) {
  try {
    const config = {
      method: test.method || 'GET',
      url: `${API_BASE}${test.endpoint}`,
      timeout: 5000
    };

    if (test.data) {
      config.data = test.data;
      config.headers = { 'Content-Type': 'application/json' };
    }

    const startTime = Date.now();
    const response = await axios(config);
    const endTime = Date.now();

    return {
      success: true,
      status: response.status,
      responseTime: endTime - startTime,
      headers: {
        rateLimit: response.headers['ratelimit-limit'],
        rateLimitRemaining: response.headers['ratelimit-remaining'],
        rateLimitReset: response.headers['ratelimit-reset']
      },
      iteration
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.message,
      headers: error.response?.headers || {},
      iteration
    };
  }
}

// Run test for a specific endpoint
async function runTest(test) {
  console.log(`\n🧪 Testing: ${test.name}`);
  console.log(`📝 Description: ${test.description}`);
  console.log(`🎯 Endpoint: ${test.endpoint}`);
  console.log(`⏱️  Running ${TEST_ITERATIONS} requests...\n`);

  const results = {
    successful: 0,
    failed: 0,
    rateLimited: 0,
    totalResponseTime: 0,
    errors: []
  };

  // Make requests in batches to avoid overwhelming the server
  const batchSize = 10;
  const batches = Math.ceil(TEST_ITERATIONS / batchSize);

  for (let batch = 0; batch < batches; batch++) {
    const batchPromises = [];
    const startIdx = batch * batchSize;
    const endIdx = Math.min(startIdx + batchSize, TEST_ITERATIONS);

    for (let i = startIdx; i < endIdx; i++) {
      batchPromises.push(makeRequest(test, i + 1));
    }

    const batchResults = await Promise.all(batchPromises);

    batchResults.forEach(result => {
      if (result.success) {
        results.successful++;
        results.totalResponseTime += result.responseTime;
      } else {
        results.failed++;
        if (result.status === 429) {
          results.rateLimited++;
        }
        results.errors.push({
          iteration: result.iteration,
          status: result.status,
          error: result.error
        });
      }
    });

    // Small delay between batches
    if (batch < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Progress indicator
    const progress = Math.round(((batch + 1) / batches) * 100);
    process.stdout.write(`\r📊 Progress: ${progress}% (${endIdx}/${TEST_ITERATIONS})`);
  }

  console.log('\n');

  // Calculate statistics
  const avgResponseTime = results.successful > 0 ? 
    Math.round(results.totalResponseTime / results.successful) : 0;
  
  const successRate = Math.round((results.successful / TEST_ITERATIONS) * 100);
  const rateLimitRate = Math.round((results.rateLimited / TEST_ITERATIONS) * 100);

  // Display results
  console.log('📈 Results:');
  console.log(`✅ Successful requests: ${results.successful}/${TEST_ITERATIONS} (${successRate}%)`);
  console.log(`❌ Failed requests: ${results.failed}/${TEST_ITERATIONS}`);
  console.log(`🚫 Rate limited: ${results.rateLimited}/${TEST_ITERATIONS} (${rateLimitRate}%)`);
  console.log(`⚡ Average response time: ${avgResponseTime}ms`);

  if (results.errors.length > 0 && results.errors.length <= 5) {
    console.log('\n🔍 Sample errors:');
    results.errors.slice(0, 5).forEach(error => {
      console.log(`  - Iteration ${error.iteration}: ${error.status} - ${error.error}`);
    });
  }

  // Assessment
  console.log('\n🎯 Assessment:');
  if (successRate >= 95) {
    console.log('✅ EXCELLENT: Very high success rate');
  } else if (successRate >= 80) {
    console.log('✅ GOOD: Good success rate');
  } else if (successRate >= 60) {
    console.log('⚠️  FAIR: Moderate success rate');
  } else {
    console.log('❌ POOR: Low success rate');
  }

  if (avgResponseTime <= 100) {
    console.log('✅ EXCELLENT: Very fast response times');
  } else if (avgResponseTime <= 500) {
    console.log('✅ GOOD: Good response times');
  } else if (avgResponseTime <= 1000) {
    console.log('⚠️  FAIR: Moderate response times');
  } else {
    console.log('❌ SLOW: Slow response times');
  }

  return results;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting API Safety and Rate Limiting Tests');
  console.log('=' .repeat(60));

  const allResults = [];

  for (const test of tests) {
    const result = await runTest(test);
    allResults.push({ test: test.name, ...result });
    
    // Wait between tests to avoid interference
    console.log('\n⏳ Waiting 5 seconds before next test...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  // Overall summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 OVERALL SUMMARY');
  console.log('='.repeat(60));

  allResults.forEach(result => {
    const successRate = Math.round((result.successful / TEST_ITERATIONS) * 100);
    const avgTime = result.successful > 0 ? 
      Math.round(result.totalResponseTime / result.successful) : 0;
    
    console.log(`${result.test}:`);
    console.log(`  Success Rate: ${successRate}% | Avg Time: ${avgTime}ms | Rate Limited: ${result.rateLimited}`);
  });

  console.log('\n✅ Testing completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  runTest,
  makeRequest
};
