const db = require('../db');
const { emitProductCreated, emitProductUpdated, emitProductDeleted } = require('../utils/webhooks');

/**
 * Product CRUD Operations
 * Handles all product-related database operations
 */

const getAllProducts = async (req, res) => {
  console.log('🔍 getAllProducts route hit');

  try {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const allowedLimits = [10, 20, 50, 100];
    const validLimit = allowedLimits.includes(limit) ? limit : 20;
    const offset = (page - 1) * validLimit;

    // Get total count for pagination info
    const countQuery = 'SELECT COUNT(*) as total FROM products';
    const [countResult] = await db.execute(countQuery);
    const totalProducts = countResult[0].total;
    const totalPages = Math.ceil(totalProducts / validLimit);

    const query = `
      SELECT
        p.*,
        p.price AS base_price,
        b.name AS brand_name,
        c.name AS category_name,
        COALESCE(p.avg_rating, 0) AS avg_rating,
        COALESCE(p.total_raters, 0) AS total_raters,
        COALESCE(p.total_sold, 0) AS total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.created_at DESC
      LIMIT ${validLimit} OFFSET ${offset}
    `;

    console.log('🛠 Executing SQL:', query);

    const [rows] = await db.query(query);

    res.json({
      success: true,
      data: rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalProducts,
        limit: validLimit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('❌ Error in getAllProducts:', error.message);
    res.status(500).json({ success: false, error: error.message });
  }
};


// Get product by ID with variants and photos
const getProductById = async (req, res) => {
  try {
    // Get product details
    const [productRows] = await db.execute(`
      SELECT
        p.*,
        p.price as base_price,
        b.name as brand_name,
        c.name as category_name,
        COALESCE(p.avg_rating, 0) as avg_rating,
        COALESCE(p.total_raters, 0) as total_raters,
        COALESCE(p.total_sold, 0) as total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `, [req.params.id]);

    if (productRows.length === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }

    // Get product variants
    const [variantRows] = await db.execute(
      'SELECT * FROM product_variants WHERE product_id = ?',
      [req.params.id]
    );

    // Get product photos
    const [photoRows] = await db.execute(
      'SELECT * FROM product_photos WHERE product_id = ?',
      [req.params.id]
    );

    const product = {
      ...productRows[0],
      variants: variantRows,
      photos: photoRows
    };

    res.json({ success: true, data: product });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Create new product
const createProduct = async (req, res) => {
  try {
    const { name, description, brand_id, category_id, base_price, total_sold, avg_rating, total_raters } = req.body;

    // Convert base_price to price (frontend uses base_price, DB uses price)
    const price = base_price || 0;
    const soldCount = total_sold || 0;
    const avgRating = avg_rating || 0;
    const totalRatersCount = total_raters || 0;

    const [result] = await db.execute(
      'INSERT INTO products (name, description, brand_id, category_id, price, total_sold, avg_rating, total_raters, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [name, description, brand_id, category_id, price, soldCount, avgRating, totalRatersCount]
    );

    const productId = result.insertId;
    const newProduct = { id: productId, name, description, brand_id, category_id, price, total_sold: soldCount, avg_rating: avgRating, total_raters: totalRatersCount };

    // Emit webhook event
    emitProductCreated(req, newProduct);

    res.status(201).json({ success: true, id: productId, message: 'Product created successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Update product
const updateProduct = async (req, res) => {
  try {
    const { name, description, brand_id, category_id, base_price, total_sold, avg_rating, total_raters } = req.body;

    // Convert base_price to price (frontend uses base_price, DB uses price)
    const price = base_price || 0;
    const soldCount = total_sold || 0;
    const avgRating = avg_rating || 0;
    const totalRatersCount = total_raters || 0;

    const [result] = await db.execute(
      'UPDATE products SET name = ?, description = ?, brand_id = ?, category_id = ?, price = ?, total_sold = ?, avg_rating = ?, total_raters = ?, updated_at = NOW() WHERE id = ?',
      [name, description, brand_id, category_id, price, soldCount, avgRating, totalRatersCount, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }

    const updatedProduct = { id: req.params.id, name, description, brand_id, category_id, price, total_sold: soldCount, avg_rating: avgRating, total_raters: totalRatersCount };

    // Emit webhook event
    emitProductUpdated(req, updatedProduct);

    res.json({ success: true, message: 'Product updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete product
const deleteProduct = async (req, res) => {
  try {
    const [result] = await db.execute('DELETE FROM products WHERE id = ?', [req.params.id]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }

    // Emit webhook event
    emitProductDeleted(req, parseInt(req.params.id));

    res.json({ success: true, message: 'Product deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get High Rating Products
const getHighRatingProducts = async (req, res) => {
  try {
    const [rows] = await db.execute(`
      SELECT
        p.*,
        p.price as base_price,
        b.name as brand_name,
        c.name as category_name,
        COALESCE(p.avg_rating, 0) as avg_rating,
        COALESCE(p.total_raters, 0) as total_raters,
        COALESCE(p.total_sold, 0) as total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.avg_rating > 0
      ORDER BY p.avg_rating DESC
      LIMIT 10
    `);


    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get High Sales Product
const getHighSalesProducts = async (req, res) => {
  try {
    const [rows] = await db.execute(`
      SELECT
        p.*,
        p.price as base_price,
        b.name as brand_name,
        c.name as category_name,
        COALESCE(p.avg_rating, 0) as avg_rating,
        COALESCE(p.total_raters, 0) as total_raters,
        COALESCE(p.total_sold, 0) as total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.total_sold > 0
      ORDER BY p.total_sold DESC
      LIMIT 10
    `);

    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get Product Based on User Input Price
const getProductsByPrice = async (req, res) => {
  try {
    const { min_price, max_price } = req.query;

    const [rows] = await db.execute(`
      SELECT
        p.*,
        p.price as base_price,
        b.name as brand_name,
        c.name as category_name,
        COALESCE(p.avg_rating, 0) as avg_rating,
        COALESCE(p.total_raters, 0) as total_raters,
        COALESCE(p.total_sold, 0) as total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.price BETWEEN ? AND ?
      ORDER BY p.price ASC
    `, [min_price, max_price]);

    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Search Products with LIKE functionality
const searchProducts = async (req, res) => {
  console.log('🔍 searchProducts route hit');

  try {
    const {
      q: searchQuery,
      page = 1,
      limit = 20,
      category_id,
      brand_id,
      min_price,
      max_price,
      sort_by = 'relevance'
    } = req.query;

    // Validate search query
    if (!searchQuery || searchQuery.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Search query must be at least 2 characters long'
      });
    }

    // Validate and sanitize pagination
    const validPage = Math.max(1, parseInt(page));
    const validLimit = Math.min(100, Math.max(1, parseInt(limit)));
    const offset = (validPage - 1) * validLimit;

    // Build search conditions
    const searchTerm = `%${searchQuery.trim()}%`;
    let whereConditions = [];
    let queryParams = [];

    // Add search conditions
    whereConditions.push(`(
      p.name LIKE ? OR
      p.description LIKE ? OR
      b.name LIKE ? OR
      c.name LIKE ?
    )`);
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);

    // Add optional filters
    if (category_id) {
      whereConditions.push('p.category_id = ?');
      queryParams.push(category_id);
    }

    if (brand_id) {
      whereConditions.push('p.brand_id = ?');
      queryParams.push(brand_id);
    }

    if (min_price) {
      whereConditions.push('p.price >= ?');
      queryParams.push(min_price);
    }

    if (max_price) {
      whereConditions.push('p.price <= ?');
      queryParams.push(max_price);
    }

    // Build ORDER BY clause
    let orderBy = 'p.created_at DESC';
    switch (sort_by) {
      case 'price_asc':
        orderBy = 'p.price ASC';
        break;
      case 'price_desc':
        orderBy = 'p.price DESC';
        break;
      case 'rating':
        orderBy = 'p.avg_rating DESC, p.total_raters DESC';
        break;
      case 'popularity':
        orderBy = 'p.total_sold DESC';
        break;
      case 'name':
        orderBy = 'p.name ASC';
        break;
      case 'relevance':
      default:
        // Relevance scoring: exact matches first, then partial matches
        orderBy = `
          CASE
            WHEN p.name LIKE ? THEN 1
            WHEN b.name LIKE ? THEN 2
            WHEN p.description LIKE ? THEN 3
            ELSE 4
          END,
          p.total_sold DESC,
          p.avg_rating DESC
        `;
        queryParams.push(searchTerm, searchTerm, searchTerm);
        break;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
    `;

    const [countResult] = await db.execute(countQuery, queryParams.slice(0, queryParams.length - (sort_by === 'relevance' ? 3 : 0)));
    const totalProducts = countResult[0].total;
    const totalPages = Math.ceil(totalProducts / validLimit);

    // Main search query
    const searchQuerySQL = `
      SELECT
        p.*,
        p.price AS base_price,
        b.name AS brand_name,
        c.name AS category_name,
        COALESCE(p.avg_rating, 0) AS avg_rating,
        COALESCE(p.total_raters, 0) AS total_raters,
        COALESCE(p.total_sold, 0) AS total_sold
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ${validLimit} OFFSET ${offset}
    `;

    console.log('🛠 Executing search SQL:', searchQuerySQL);
    console.log('🛠 Search params:', queryParams);

    const [rows] = await db.execute(searchQuerySQL, queryParams);

    res.json({
      success: true,
      data: rows,
      search: {
        query: searchQuery,
        total_results: totalProducts
      },
      pagination: {
        currentPage: validPage,
        totalPages,
        totalProducts,
        limit: validLimit,
        hasNextPage: validPage < totalPages,
        hasPrevPage: validPage > 1
      }
    });

  } catch (error) {
    console.error('❌ Search products error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search products',
      details: error.message
    });
  }
};

// Get Products for View-Based Loading (No Pagination)
const getProductsForView = async (req, res) => {
  console.log('🔍 getProductsForView route hit');

  try {
    const {
      offset = 0,
      limit = 20,
      category_id,
      brand_id,
      min_price,
      max_price,
      sort_by = 'created_at'
    } = req.query;

    // Validate and sanitize parameters
    const validOffset = Math.max(0, parseInt(offset));
    const validLimit = Math.min(50, Math.max(1, parseInt(limit))); // Max 50 items per request for performance

    // Build filter conditions
    let whereConditions = [];
    let queryParams = [];

    if (category_id) {
      whereConditions.push('p.category_id = ?');
      queryParams.push(category_id);
    }

    if (brand_id) {
      whereConditions.push('p.brand_id = ?');
      queryParams.push(brand_id);
    }

    if (min_price) {
      whereConditions.push('p.price >= ?');
      queryParams.push(min_price);
    }

    if (max_price) {
      whereConditions.push('p.price <= ?');
      queryParams.push(max_price);
    }

    // Build ORDER BY clause
    let orderBy = 'p.created_at DESC';
    switch (sort_by) {
      case 'price_asc':
        orderBy = 'p.price ASC';
        break;
      case 'price_desc':
        orderBy = 'p.price DESC';
        break;
      case 'rating':
        orderBy = 'p.avg_rating DESC, p.total_raters DESC';
        break;
      case 'popularity':
        orderBy = 'p.total_sold DESC';
        break;
      case 'name':
        orderBy = 'p.name ASC';
        break;
      case 'created_at':
      default:
        orderBy = 'p.created_at DESC';
        break;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Lightweight query - only essential fields for browsing
    const query = `
      SELECT
        p.id,
        p.name,
        p.price,
        p.photo_url,
        COALESCE(p.avg_rating, 0) AS avg_rating,
        COALESCE(p.total_raters, 0) AS total_raters,
        COALESCE(p.total_sold, 0) AS total_sold,
        b.name AS brand_name,
        c.name AS category_name
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ${validLimit} OFFSET ${validOffset}
    `;

    console.log('🛠 Executing view-based SQL:', query);
    console.log('🛠 View params:', queryParams);

    const [rows] = await db.execute(query, queryParams);

    // Check if there are more items available
    const hasMore = rows.length === validLimit;

    res.json({
      success: true,
      data: rows,
      meta: {
        offset: validOffset,
        limit: validLimit,
        returned: rows.length,
        hasMore,
        nextOffset: hasMore ? validOffset + validLimit : null
      }
    });

  } catch (error) {
    console.error('❌ Get products for view error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load products',
      details: error.message
    });
  }
};

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getHighRatingProducts,
  getHighSalesProducts,
  getProductsByPrice,
  searchProducts,
  getProductsForView
};
