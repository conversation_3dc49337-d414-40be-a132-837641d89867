/**
 * Batch API Routes
 * Allows clients to make multiple API calls in a single request
 * This helps reduce the number of individual requests and avoid rate limiting
 */

const express = require('express');
const router = express.Router();

// Import CRUD modules for batch operations
const brandCRUD = require('../database/brandCRUD');
const categoryCRUD = require('../database/categoryCRUD');
const productCRUD = require('../database/productCRUD');
const variantCRUD = require('../database/variantCRUD');
const photoCRUD = require('../database/photoCRUD');
const ratingCRUD = require('../database/ratingCRUD');
const bannerCRUD = require('../database/bannerCRUD');

// Batch request handler
const handleBatchRequest = async (req, res) => {
  try {
    const { requests } = req.body;
    
    if (!Array.isArray(requests) || requests.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Requests array is required and must not be empty'
      });
    }
    
    if (requests.length > 10) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 10 requests allowed per batch'
      });
    }
    
    const results = [];
    
    for (const request of requests) {
      try {
        const result = await processSingleRequest(request, req);
        results.push({
          id: request.id,
          success: true,
          data: result
        });
      } catch (error) {
        results.push({
          id: request.id,
          success: false,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      results: results
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Batch processing failed',
      details: error.message
    });
  }
};

// Process individual request within batch
const processSingleRequest = async (request, originalReq) => {
  const { method, endpoint, params = {}, query = {} } = request;
  
  if (method !== 'GET') {
    throw new Error('Only GET requests are supported in batch operations');
  }
  
  // Create mock request object
  const mockReq = {
    ...originalReq,
    params: params,
    query: query,
    path: endpoint
  };
  
  // Create mock response object
  let responseData = null;
  let statusCode = 200;
  
  const mockRes = {
    json: (data) => {
      responseData = data;
      return mockRes;
    },
    status: (code) => {
      statusCode = code;
      return mockRes;
    },
    statusCode: 200
  };
  
  // Route to appropriate handler based on endpoint
  switch (endpoint) {
    case '/api/brands':
      await brandCRUD.getAllBrands(mockReq, mockRes);
      break;
    case '/api/brands/:id':
      await brandCRUD.getBrandById(mockReq, mockRes);
      break;
    case '/api/categories':
      await categoryCRUD.getAllCategories(mockReq, mockRes);
      break;
    case '/api/categories/:id':
      await categoryCRUD.getCategoryById(mockReq, mockRes);
      break;
    case '/api/products':
      await productCRUD.getAllProducts(mockReq, mockRes);
      break;
    case '/api/products/:id':
      await productCRUD.getProductById(mockReq, mockRes);
      break;
    case '/api/products/high-sales':
      await productCRUD.getHighSalesProducts(mockReq, mockRes);
      break;
    case '/api/products/high-rating':
      await productCRUD.getHighRatingProducts(mockReq, mockRes);
      break;
    case '/api/products/price':
      await productCRUD.getProductsByPrice(mockReq, mockRes);
      break;
    case '/api/products/:productId/variants':
      await variantCRUD.getVariantsByProductId(mockReq, mockRes);
      break;
    case '/api/products/:productId/photos':
      await photoCRUD.getPhotosByProductId(mockReq, mockRes);
      break;
    case '/api/products/:productId/ratings':
      await ratingCRUD.getRatingsByProductId(mockReq, mockRes);
      break;
    case '/api/banners':
      await bannerCRUD.getAllBanners(mockReq, mockRes);
      break;
    case '/api/banners/active':
      await bannerCRUD.getActiveBanners(mockReq, mockRes);
      break;
    default:
      throw new Error(`Unsupported endpoint: ${endpoint}`);
  }
  
  if (statusCode >= 400) {
    throw new Error(responseData?.error || 'Request failed');
  }
  
  return responseData;
};

module.exports = {
  handleBatchRequest,
  router
};
