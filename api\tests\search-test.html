<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-box {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .search-box:focus {
            outline: none;
            border-color: #ffcd29;
        }
        .btn {
            background: #ffcd29;
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #fbbf24;
        }
        .results {
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .product-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .product-price {
            color: #059669;
            font-weight: bold;
            font-size: 16px;
        }
        .product-brand {
            color: #666;
            font-style: italic;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background: #fee;
            color: #c00;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background: #efe;
            color: #060;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        .filter-group label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .filter-group input, .filter-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔍 Search API Test Page</h1>
    
    <div class="test-container">
        <h2>Search Products</h2>
        <input type="text" id="searchInput" class="search-box" placeholder="Search for products..." value="case">
        
        <div class="filters">
            <div class="filter-group">
                <label>Min Price (IDR)</label>
                <input type="number" id="minPrice" placeholder="100000">
            </div>
            <div class="filter-group">
                <label>Max Price (IDR)</label>
                <input type="number" id="maxPrice" placeholder="500000">
            </div>
            <div class="filter-group">
                <label>Sort By</label>
                <select id="sortBy">
                    <option value="relevance">Relevance</option>
                    <option value="price_asc">Price: Low to High</option>
                    <option value="price_desc">Price: High to Low</option>
                    <option value="rating">Rating</option>
                    <option value="popularity">Popularity</option>
                    <option value="name">Name</option>
                </select>
            </div>
        </div>
        
        <button class="btn" onclick="searchProducts()">Search Products</button>
        <button class="btn" onclick="loadViewProducts()">Load View Products</button>
        <button class="btn" onclick="clearResults()">Clear Results</button>
        
        <div id="status"></div>
        <div id="results" class="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        function showLoading() {
            document.getElementById('results').innerHTML = '<div class="loading">Loading...</div>';
        }
        
        function formatRupiah(amount) {
            return new Intl.NumberFormat("id-ID", {
                style: "currency",
                currency: "IDR",
                minimumFractionDigits: 0,
            }).format(amount);
        }
        
        function renderProducts(products, searchInfo = null) {
            const resultsDiv = document.getElementById('results');
            
            if (products.length === 0) {
                resultsDiv.innerHTML = '<p>No products found.</p>';
                return;
            }
            
            let html = '';
            
            if (searchInfo) {
                html += `<p><strong>Search Results for "${searchInfo.query}": ${searchInfo.total_results} products found</strong></p>`;
            }
            
            html += products.map(product => `
                <div class="product-card">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${formatRupiah(product.price)}</div>
                    ${product.brand_name ? `<div class="product-brand">${product.brand_name}</div>` : ''}
                    <div>⭐ ${product.avg_rating ? product.avg_rating.toFixed(1) : 'No rating'} | ${product.total_sold || 0} sold</div>
                </div>
            `).join('');
            
            resultsDiv.innerHTML = html;
        }
        
        async function searchProducts() {
            const query = document.getElementById('searchInput').value.trim();
            
            if (query.length < 2) {
                showStatus('Please enter at least 2 characters', 'error');
                return;
            }
            
            showLoading();
            showStatus('Searching products...', 'info');
            
            try {
                const params = new URLSearchParams({
                    q: query,
                    limit: 20
                });
                
                // Add filters if provided
                const minPrice = document.getElementById('minPrice').value;
                const maxPrice = document.getElementById('maxPrice').value;
                const sortBy = document.getElementById('sortBy').value;
                
                if (minPrice) params.append('min_price', minPrice);
                if (maxPrice) params.append('max_price', maxPrice);
                if (sortBy) params.append('sort_by', sortBy);
                
                const response = await fetch(`${API_BASE}/products/search?${params}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP ${response.status}`);
                }
                
                if (data.success) {
                    renderProducts(data.data, data.search);
                    showStatus(`Found ${data.data.length} products`, 'success');
                } else {
                    throw new Error(data.error || 'Search failed');
                }
                
            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
                document.getElementById('results').innerHTML = '';
            }
        }
        
        async function loadViewProducts() {
            showLoading();
            showStatus('Loading products for view...', 'info');
            
            try {
                const params = new URLSearchParams({
                    offset: 0,
                    limit: 20,
                    sort_by: 'created_at'
                });
                
                const response = await fetch(`${API_BASE}/products/view?${params}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP ${response.status}`);
                }
                
                if (data.success) {
                    renderProducts(data.data);
                    showStatus(`Loaded ${data.data.length} products (Has more: ${data.meta.hasMore})`, 'success');
                } else {
                    throw new Error(data.error || 'Load failed');
                }
                
            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
                document.getElementById('results').innerHTML = '';
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('status').innerHTML = '';
        }
        
        // Allow Enter key to trigger search
        document.getElementById('searchInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });
        
        // Load some products on page load
        window.addEventListener('load', function() {
            loadViewProducts();
        });
    </script>
</body>
</html>
