import safeApiClient from '../../lib/SafeApiClient.js';
import { TopNavigationBar, SearchHandler } from '../../components/TopNavigationBar.js';
import { BottomNavigationBar } from '../../components/BottomNavigationBar.js';
import { Footer } from '../../components/Footer.js';
import '../../styles/components/katalog.css';

class KatalogPage {
    constructor() {
        this.safeApiClient = safeApiClient;
        this.searchHandler = new SearchHandler();
        this.products = [];
        this.currentOffset = 0;
        this.isLoading = false;
        this.hasMore = true;
        this.searchQuery = '';
        this.filters = {
            category_id: null,
            brand_id: null,
            min_price: null,
            max_price: null,
            sort_by: 'created_at'
        };
    }

    async render(container) {
        container.innerHTML = this.getHTML();
        this.setUpNavigation();
        this.bindEvents();

        // Check for search parameter in URL
        const urlParams = new URLSearchParams(window.location.search);
        this.searchQuery = urlParams.get('search') || '';

        await this.loadProducts();

        // Initialize search functionality
        setTimeout(() => {
            this.searchHandler.init();
            // Pre-fill search input if there's a search query
            if (this.searchQuery) {
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.value = this.searchQuery;
                }
            }
        }, 100);
    }

    setUpNavigation() {
        document.getElementById('top-bar').innerHTML = TopNavigationBar();
        document.getElementById('bottom-bar').innerHTML = BottomNavigationBar();
        document.getElementById('footer').innerHTML = Footer();
    }

    getHTML() {
        return `
            <div id="top-bar"></div>
                <div class="filter-container">
                    <div class="filter-row-2col">
                        <div class="filter-group">
                            <label class="filter-label">Harga</label>
                            <div class="filter-row">
                                <label class="filter-sub">Min</label>
                                <input type="number" class="filter-input" placeholder="Rp 20.000">
                            </div>
                            <div class="filter-row">
                                <label class="filter-sub">Maks</label>
                                <input type="number" class="filter-input" placeholder="Rp 500.000">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Status</label>
                            <div class="filter-row">
                                <select class="filter-input-status">
                                <option>Terbaik</option>
                                </select>
                            </div>
                            <div class="filter-row">
                                <select class="filter-input-status">
                                <option>Default</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Berdasarkan Produk</label>
                        <select class="filter-input-status">
                            <option>Semua</option>
                        </select>
                    </div>
                    </div>
                    <div class="divider"></div>
                    <div class="card-container">
                        <!-- Products will be loaded here -->
                    </div>
                    <div class="loading-indicator" style="display: none;">
                        <p>Loading more products...</p>
                    </div>
                    <div class="no-more-products" style="display: none;">
                        <p>No more products to load</p>
                    </div>

            </div>


            <div id="footer"></div>
            <div id="bottom-bar"></div>
        `;
    }

    bindEvents() {
        // Set up infinite scroll
        this.setupInfiniteScroll();

        // Set up filter interactions
        this.setupFilters();

        // Navigation active states
        const navLinks = document.querySelectorAll('.nav-links a');
        const currentPath = window.location.pathname;

        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });

        const links = document.querySelectorAll(".bottom-nav a");
        links.forEach(link => {
            if (link.getAttribute("data-path").toLowerCase() === currentPath) {
                link.classList.add("active");
            }
        });
    }

    async loadProducts(reset = true) {
        if (this.isLoading) return;

        this.isLoading = true;
        const container = document.querySelector('.card-container');
        const loadingIndicator = document.querySelector('.loading-indicator');
        const noMoreIndicator = document.querySelector('.no-more-products');

        if (reset) {
            this.currentOffset = 0;
            this.hasMore = true;
            this.products = [];
            container.innerHTML = '';
            noMoreIndicator.style.display = 'none';
        }

        if (!this.hasMore) {
            this.isLoading = false;
            return;
        }

        loadingIndicator.style.display = 'block';

        try {
            let response;

            if (this.searchQuery) {
                // Use search API
                response = await this.safeApiClient.get('/products/search', {
                    params: {
                        q: this.searchQuery,
                        offset: this.currentOffset,
                        limit: 20,
                        ...this.filters
                    }
                });
            } else {
                // Use view-based API for browsing
                response = await this.safeApiClient.get('/products/view', {
                    params: {
                        offset: this.currentOffset,
                        limit: 20,
                        ...this.filters
                    }
                });
            }

            const newProducts = response.data || [];
            this.products = [...this.products, ...newProducts];

            // Update hasMore based on response
            if (this.searchQuery) {
                // For search, check pagination
                this.hasMore = response.pagination && response.pagination.hasNextPage;
            } else {
                // For view-based, check meta
                this.hasMore = response.meta && response.meta.hasMore;
            }

            this.currentOffset += newProducts.length;

            // Render new products
            this.renderProducts(newProducts, !reset);

            if (!this.hasMore) {
                noMoreIndicator.style.display = 'block';
            }

        } catch (err) {
            console.error('Error loading products:', err);
            if (reset) {
                container.innerHTML = `<p>Gagal memuat produk. Silakan coba lagi.</p>`;
            }
        } finally {
            this.isLoading = false;
            loadingIndicator.style.display = 'none';
        }
    }

    renderProducts(products, append = false) {
        const container = document.querySelector('.card-container');

        const productsHTML = products.map(product => `
            <div class="card" data-product-id="${product.id}">
                <img src="${product.photo_url || '/assets/placeholder.png'}" alt="${product.name}" class="card-img"/>
                <div class="card-content">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-price">${this.formatRupiah(product.price)}</p>
                    <div class="card-rating">
                        <span>⭐ ${product.avg_rating ? product.avg_rating.toFixed(1) : 'Belum ada rating'}</span>
                        <span>|</span>
                        <span>${product.total_sold ?? 0}+ Terjual</span>
                    </div>
                    ${product.brand_name ? `<p class="product-brand">${product.brand_name}</p>` : ''}
                </div>
            </div>
        `).join('');

        if (append) {
            container.innerHTML += productsHTML;
        } else {
            container.innerHTML = productsHTML;
        }

        // Add click events to product cards
        this.bindProductCardEvents();
    }

    bindProductCardEvents() {
        const productCards = document.querySelectorAll('.card[data-product-id]');
        productCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const productId = e.currentTarget.dataset.productId;
                window.location.href = `/product/${productId}`;
            });
        });
    }

    setupInfiniteScroll() {
        let ticking = false;

        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const windowHeight = window.innerHeight;
                    const documentHeight = document.documentElement.scrollHeight;

                    // Load more when user is 200px from bottom
                    if (scrollTop + windowHeight >= documentHeight - 200) {
                        this.loadProducts(false);
                    }

                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', handleScroll);

        // Store reference for cleanup
        this.scrollHandler = handleScroll;
    }

    setupFilters() {
        // Price filter inputs
        const minPriceInput = document.querySelector('.filter-input[placeholder*="20.000"]');
        const maxPriceInput = document.querySelector('.filter-input[placeholder*="500.000"]');

        if (minPriceInput && maxPriceInput) {
            const applyPriceFilter = () => {
                const minPrice = parseFloat(minPriceInput.value) || null;
                const maxPrice = parseFloat(maxPriceInput.value) || null;

                if (minPrice !== this.filters.min_price || maxPrice !== this.filters.max_price) {
                    this.filters.min_price = minPrice;
                    this.filters.max_price = maxPrice;
                    this.loadProducts(true);
                }
            };

            minPriceInput.addEventListener('blur', applyPriceFilter);
            maxPriceInput.addEventListener('blur', applyPriceFilter);

            // Also apply on Enter key
            minPriceInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') applyPriceFilter();
            });
            maxPriceInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') applyPriceFilter();
            });
        }

        // Status/Sort filters
        const sortSelects = document.querySelectorAll('.filter-input-status');
        sortSelects.forEach(select => {
            select.addEventListener('change', (e) => {
                const value = e.target.value.toLowerCase();
                let sortBy = 'created_at';

                switch (value) {
                    case 'terbaik':
                        sortBy = 'rating';
                        break;
                    case 'terpopuler':
                        sortBy = 'popularity';
                        break;
                    case 'termurah':
                        sortBy = 'price_asc';
                        break;
                    case 'termahal':
                        sortBy = 'price_desc';
                        break;
                    default:
                        sortBy = 'created_at';
                }

                if (sortBy !== this.filters.sort_by) {
                    this.filters.sort_by = sortBy;
                    this.loadProducts(true);
                }
            });
        });
    }

    formatRupiah(angka) {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(angka);
    }

    // Cleanup method
    destroy() {
        if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
        }
        if (this.searchHandler) {
            this.searchHandler.destroy();
        }
    }
}

export default KatalogPage;
