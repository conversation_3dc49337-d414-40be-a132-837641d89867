/**
 * Cache Control Middleware
 * Provides different caching strategies for different types of endpoints
 */

// Cache control for public static data (brands, categories, banners)
const publicStaticCache = (req, res, next) => {
  // Cache for 5 minutes, allow stale content for 1 hour
  res.set({
    'Cache-Control': 'public, max-age=300, stale-while-revalidate=3600',
    'Vary': 'Accept-Encoding'
  });
  next();
};

// Cache control for dynamic product data
const productDataCache = (req, res, next) => {
  // Cache for 2 minutes, allow stale content for 30 minutes
  res.set({
    'Cache-Control': 'public, max-age=120, stale-while-revalidate=1800',
    'Vary': 'Accept-Encoding'
  });
  next();
};

// Cache control for frequently changing data (ratings, variants)
const dynamicDataCache = (req, res, next) => {
  // Cache for 1 minute, allow stale content for 10 minutes
  res.set({
    'Cache-Control': 'public, max-age=60, stale-while-revalidate=600',
    'Vary': 'Accept-Encoding'
  });
  next();
};

// Cache control for admin data (should not be cached publicly)
const adminDataCache = (req, res, next) => {
  // Private cache only, very short duration
  res.set({
    'Cache-Control': 'private, max-age=30, no-cache',
    'Vary': 'Accept-Encoding'
  });
  next();
};

// No cache for sensitive or real-time data
const noCache = (req, res, next) => {
  res.set({
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Surrogate-Control': 'no-store'
  });
  next();
};

// ETag support for conditional requests
const conditionalCache = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    if (res.statusCode === 200 && data) {
      // Generate simple ETag based on content
      const etag = `"${Buffer.from(JSON.stringify(data)).toString('base64').slice(0, 16)}"`;
      res.set('ETag', etag);
      
      // Check if client has the same version
      const clientETag = req.headers['if-none-match'];
      if (clientETag === etag) {
        res.status(304).end();
        return;
      }
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  publicStaticCache,
  productDataCache,
  dynamicDataCache,
  adminDataCache,
  noCache,
  conditionalCache
};
